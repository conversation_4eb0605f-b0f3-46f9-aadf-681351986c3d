using Business.Abstract;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Entities.Concrete;
using Core.Extensions;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using Core.Utilities.Security.JWT;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class AuthManager : IAuthService
    {
        private IUserService _userService;
        private ITokenHelper _tokenHelper;
        private IUserDeviceService _userDeviceService;
        private IHttpContextAccessor _httpContextAccessor;
        private IUserLicenseService _userLicenseService;
        private IUserCompanyService _userCompanyService;
        private IUserOperationClaimService _userOperationClaimService;
        private IOperationClaimService _operationClaimService;
        private IAdvancedRateLimitService _advancedRateLimitService;
        private const int MAX_ACTIVE_DEVICES = 5;

        public AuthManager(
            IUserService userService,
            ITokenHelper tokenHelper,
            IUserDeviceService userDeviceService,
            IHttpContextAccessor httpContextAccessor,
            IUserLicenseService userLicenseService,
            IUserCompanyService userCompanyService,
            IUserOperationClaimService userOperationClaimService,
            IOperationClaimService operationClaimService,
            IAdvancedRateLimitService advancedRateLimitService)
        {
            _userService = userService;
            _tokenHelper = tokenHelper;
            _userDeviceService = userDeviceService;
            _httpContextAccessor = httpContextAccessor;
            _userLicenseService = userLicenseService;
            _userCompanyService = userCompanyService;
            _userOperationClaimService = userOperationClaimService;
            _operationClaimService = operationClaimService;
            _advancedRateLimitService = advancedRateLimitService;
        }

        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IDataResult<User> Register(UserForRegisterDto userForRegisterDto, string password)
        {
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown";

            // Register rate limiting kontrolü
            var rateLimitCheck = _advancedRateLimitService.CheckRegisterAttempt(ipAddress);
            if (!rateLimitCheck.Success)
            {
                return new ErrorDataResult<User>(null, rateLimitCheck.Message);
            }

            try
            {
                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(password, out passwordHash, out passwordSalt);
                var user = new User
                {
                    Email = userForRegisterDto.Email,
                    FirstName = userForRegisterDto.FirstName,
                    LastName = userForRegisterDto.LastName,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };
                _userService.Add(user);

                // Başarılı kayıt sonrası rate limit sayacını güncelle
                _advancedRateLimitService.RecordSuccessfulRegister(ipAddress);

                return new SuccessDataResult<User>(user, Messages.UserRegistered);
            }
            catch
            {
                return new ErrorDataResult<User>(null, "Kayıt işlemi başarısız oldu.");
            }
        }

        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IDataResult<User> RegisterMember(MemberForRegisterDto memberForRegisterDto, string password)
        {
            // IP adresi ve zaman damgası gibi bilgileri loglama
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
            var timestamp = DateTime.Now;

            // Log mesajı
            Console.WriteLine($"[{timestamp}] RegisterMember called: Email={memberForRegisterDto.Email}, FirstName={memberForRegisterDto.FirstName}, LastName={memberForRegisterDto.LastName}, IP: {ipAddress}");

            // Register rate limiting kontrolü
            var rateLimitCheck = _advancedRateLimitService.CheckRegisterAttempt(ipAddress);
            if (!rateLimitCheck.Success)
            {
                return new ErrorDataResult<User>(null, rateLimitCheck.Message);
            }

            // Mevcut User var mı kontrol et
            var existingUser = _userService.GetByMail(memberForRegisterDto.Email);

            User user;

            if (existingUser != null)
            {
                // Mevcut User bulundu
                user = existingUser;

                // Eğer RequirePasswordChange=true ise kullanıcının girdiği şifreyi kullan
                if (user.RequirePasswordChange)
                {
                    // Kullanıcının girdiği şifre ile hash oluştur
                    byte[] passwordHash, passwordSalt;
                    HashingHelper.CreatePasswordHash(password, out passwordHash, out passwordSalt);

                    // User bilgilerini güncelle
                    user.PasswordHash = passwordHash;
                    user.PasswordSalt = passwordSalt;
                    user.RequirePasswordChange = false; // Artık şifre değiştirme gerekmiyor
                    user.UpdatedDate = DateTime.Now;

                    _userService.Update(user);
                }
                // Eğer RequirePasswordChange=false ise şifre değiştirme, mevcut şifreyi kullan
            }
            else
            {
                // Yeni User oluştur
                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(password, out passwordHash, out passwordSalt);
                user = new User
                {
                    Email = memberForRegisterDto.Email,
                    FirstName = memberForRegisterDto.FirstName,
                    LastName = memberForRegisterDto.LastName,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsActive = true,
                    RequirePasswordChange = false,
                    CreationDate = DateTime.Now
                };
                _userService.Add(user);

                // "member" rolünü al - güvenlik kontrolü olmayan metodu kullan
                var memberRoleResult = _operationClaimService.GetByName("member");
                if (memberRoleResult.Success && memberRoleResult.Data != null)
                {
                    // Kullanıcıya "member" rolünü ata - güvenlik kontrolü olmayan metodu kullan
                    var userOperationClaim = new UserOperationClaim
                    {
                        UserId = user.UserID,
                        OperationClaimId = memberRoleResult.Data.OperationClaimId,
                        IsActive = true,
                        CreationDate = DateTime.Now
                    };
                    _userOperationClaimService.AddForRegistration(userOperationClaim);
                }
            }

            // Başarılı kayıt sonrası rate limit sayacını güncelle
            _advancedRateLimitService.RecordSuccessfulRegister(ipAddress);

            return new SuccessDataResult<User>(user, Messages.MemberRegistered);
        }

        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IDataResult<User> Login(UserForLoginDto userForLoginDto, string deviceInfo)
        {
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown";
            var userAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString() ?? "unknown";

            // Device fingerprint oluştur
            var deviceFingerprint = _advancedRateLimitService.GenerateDeviceFingerprint(ipAddress, userAgent, deviceInfo);

            // Login rate limiting kontrolü - BAN durumunda işlemi tamamen durdur
            var rateLimitCheck = _advancedRateLimitService.CheckLoginAttempt(ipAddress, deviceFingerprint);
            if (!rateLimitCheck.Success)
            {
                // Ban durumunda hiçbir işlem yapma, kullanıcı kontrolü bile yapma
                // Bu sayede doğru şifre girmiş olsa bile ban devam eder
                return new ErrorDataResult<User>(null, rateLimitCheck.Message);
            }

            var userToCheck = _userService.GetByMail(userForLoginDto.Email);
            if (userToCheck == null)
            {
                // Kullanıcı bulunamadı - başarısız giriş kaydı
                _advancedRateLimitService.RecordFailedLogin(ipAddress, deviceFingerprint);
                return new ErrorDataResult<User>(null, Messages.UserNotFound);
            }

            if (!HashingHelper.VerifyPasswordHash(userForLoginDto.Password, userToCheck.PasswordHash, userToCheck.PasswordSalt))
            {
                // Şifre hatalı - başarısız giriş kaydı
                _advancedRateLimitService.RecordFailedLogin(ipAddress, deviceFingerprint);
                return new ErrorDataResult<User>(null, Messages.PasswordError);
            }

            // Kullanıcı aktif mi kontrolü
            if (!userToCheck.IsActive)
            {
                // Pasif kullanıcı - başarısız giriş kaydı
                _advancedRateLimitService.RecordFailedLogin(ipAddress, deviceFingerprint);
                return new ErrorDataResult<User>(null, "Hesabınız pasif durumda. Lütfen yönetici ile iletişime geçin.");
            }

            // Bu noktaya geldiysek giriş başarılı - rate limit sayacını sıfırla
            _advancedRateLimitService.RecordSuccessfulLogin(ipAddress, deviceFingerprint);

            // Şifre değiştirme zorunluluğu kontrolü
            if (userToCheck.RequirePasswordChange)
            {
                return new SuccessDataResult<User>(userToCheck, "PasswordChangeRequired");
            }

            return new SuccessDataResult<User>(userToCheck, Messages.SuccessfulLogin);
        }



        private string GetCurrentIPAddress()
        {
            return _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
        }

        public IResult UserExists(string email)
        {
            if (_userService.GetByMail(email) != null)
            {
                return new ErrorResult(Messages.UserAlreadyExists);
            }
            return new SuccessResult();
        }

        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult ChangePassword(int userId, string currentPassword, string newPassword)
        {
            // Kullanıcıyı kontrol et
            var userResult = _userService.GetById(userId);
            if (!userResult.Success)
            {
                return new ErrorResult(Messages.UserNotFound);
            }

            var user = userResult.Data;

            // Mevcut şifreyi doğrula
            if (!HashingHelper.VerifyPasswordHash(currentPassword, user.PasswordHash, user.PasswordSalt))
            {
                return new ErrorResult(Messages.PasswordError);
            }

            // Yeni şifre için minimum 6 karakter kontrolü
            if (newPassword.Length < 6)
            {
                return new ErrorResult("Şifre en az 6 karakter olmalıdır.");
            }

            // Yeni şifre hash'i oluştur
            byte[] passwordHash, passwordSalt;
            HashingHelper.CreatePasswordHash(newPassword, out passwordHash, out passwordSalt);

            // Kullanıcı bilgilerini güncelle
            user.PasswordHash = passwordHash;
            user.PasswordSalt = passwordSalt;
            user.RequirePasswordChange = false;
            user.UpdatedDate = DateTime.Now;

            // Kullanıcıyı güncelle
            var updateResult = _userService.Update(user);
            if (!updateResult.Success)
            {
                return new ErrorResult(updateResult.Message);
            }

            return new SuccessResult("Şifreniz başarıyla değiştirildi.");
        }

        [PerformanceAspect(3)]
        public IDataResult<bool> CheckPasswordChangeRequired(int userId)
        {
            // Kullanıcıyı kontrol et
            var userResult = _userService.GetById(userId);
            if (!userResult.Success)
            {
                return new ErrorDataResult<bool>(false, Messages.UserNotFound);
            }

            return new SuccessDataResult<bool>(userResult.Data.RequirePasswordChange);
        }

        [PerformanceAspect(2)]
        public IDataResult<AccessToken> CreateAccessToken(User user, string deviceInfo)
        {
            // Get default claims from user service
            var defaultClaims = _userService.GetClaims(user);

            // Get additional claims from user licenses
            var licensedRoles = _userLicenseService.GetUserRoles(user.UserID).Data;

            // Combine all claims
            var allClaims = new List<OperationClaim>(defaultClaims);

            foreach (var role in licensedRoles)
            {
                if (!allClaims.Any(c => c.Name == role))
                {
                    allClaims.Add(new OperationClaim { Name = role });
                }
            }

            // Kullanıcının şirket ID'sini al
            var companyIdResult = _userCompanyService.GetUserCompanyId(user.UserID);
            int companyId = companyIdResult.Success ? companyIdResult.Data : -1;

            // Token oluştur
            var accessToken = _tokenHelper.CreateToken(user, allClaims, companyId);
            var refreshToken = _tokenHelper.CreateRefreshToken(user);

            var userDevice = new UserDevice
            {
                UserId = user.UserID,
                DeviceInfo = deviceInfo,
                RefreshToken = refreshToken.Token,
                LastIpAddress = GetCurrentIPAddress(),
                RefreshTokenExpiration = refreshToken.Expiration,
                IsActive = true,
                CreatedAt = DateTime.Now,
                LastUsedAt = DateTime.Now
            };

            _userDeviceService.Add(userDevice);

            accessToken.RefreshToken = refreshToken.Token;
            return new SuccessDataResult<AccessToken>(accessToken, Messages.SuccessfulLogin);
        }

        public IDataResult<AccessToken> CreateAccessTokenWithRefreshToken(string refreshToken, string ipAddress, string deviceInfo)
        {
            var userDevice = _userDeviceService.GetByRefreshToken(refreshToken);
            if (!userDevice.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.InvalidRefreshToken);
            }

            var device = userDevice.Data;

            // Cihazın aktif olup olmadığını kontrol et (başka cihazdan giriş yapılmış olabilir)
            if (!device.IsActive)
            {
                return new ErrorDataResult<AccessToken>("DEVICE_REVOKED: Bu cihazdan oturumunuz sonlandırılmıştır. Hesabınıza başka bir cihazdan giriş yapılmış olabilir.");
            }

            if (device.RefreshTokenExpiration <= DateTime.Now)
            {
                // Immediately revoke the expired token
                _userDeviceService.RevokeDevice(device.Id);
                return new ErrorDataResult<AccessToken>(Messages.ExpiredRefreshToken);
            }

            var userResult = _userService.GetById(device.UserId);
            if (!userResult.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.UserNotFound);
            }

            // Get default claims from user service
            var defaultClaims = _userService.GetClaims(userResult.Data);

            // Get additional claims from user licenses
            var licensedRoles = _userLicenseService.GetUserRoles(userResult.Data.UserID).Data;

            // Combine all claims
            var allClaims = new List<OperationClaim>(defaultClaims);

            foreach (var role in licensedRoles)
            {
                if (!allClaims.Any(c => c.Name == role))
                {
                    allClaims.Add(new OperationClaim { Name = role });
                }
            }

            // Kullanıcının şirket ID'sini al
            var companyIdResult = _userCompanyService.GetUserCompanyId(userResult.Data.UserID);
            int companyId = companyIdResult.Success ? companyIdResult.Data : -1;

            var accessToken = _tokenHelper.CreateToken(userResult.Data, allClaims, companyId);
            var newRefreshToken = _tokenHelper.CreateRefreshToken(userResult.Data);

            device.RefreshToken = newRefreshToken.Token;
            device.LastIpAddress = ipAddress;
            device.LastUsedAt = DateTime.Now;
            device.DeviceInfo = deviceInfo;

            _userDeviceService.Update(device);

            accessToken.RefreshToken = newRefreshToken.Token;
            return new SuccessDataResult<AccessToken>(accessToken, Messages.TokensRefreshed);
        }

        public IResult RevokeRefreshToken(string refreshToken)
        {
            var userDevice = _userDeviceService.GetByRefreshToken(refreshToken);
            if (!userDevice.Success)
            {
                return new ErrorResult(Messages.InvalidRefreshToken);
            }

            return _userDeviceService.RevokeDevice(userDevice.Data.Id);
        }

        public IResult RevokeAllDevices(int userId)
        {
            var currentRefreshToken = _httpContextAccessor.HttpContext?.Request?.Headers["X-Refresh-Token"].ToString();
            return _userDeviceService.RevokeAllDevicesExceptCurrent(userId, currentRefreshToken);
        }

        public IResult RevokeDevice(int deviceId)
        {
            return _userDeviceService.RevokeDevice(deviceId);
        }

        public IDataResult<List<UserDeviceDto>> GetUserDevices(int userId)
        {
            var devices = _userDeviceService.GetActiveDevicesByUserId(userId);
            if (!devices.Success)
            {
                return new ErrorDataResult<List<UserDeviceDto>>(devices.Message);
            }

            // Mevcut refresh token'ı al
            var currentRefreshToken = _httpContextAccessor.HttpContext?.Request?.Headers["X-Refresh-Token"].ToString();

            var deviceDtos = devices.Data.Select(d => new UserDeviceDto
            {
                Id = d.Id,
                DeviceInfo = d.DeviceInfo,
                LastIpAddress = d.LastIpAddress,
                CreatedAt = d.CreatedAt,
                LastUsedAt = d.LastUsedAt,
                IsCurrentDevice = !string.IsNullOrEmpty(currentRefreshToken) && d.RefreshToken == currentRefreshToken
            }).OrderByDescending(d => d.IsCurrentDevice).ThenByDescending(d => d.LastUsedAt).ToList();

            return new SuccessDataResult<List<UserDeviceDto>>(deviceDtos);
        }

        [PerformanceAspect(3)]
        public IDataResult<AccessToken> ChangeCompany(int userId, int companyId, string deviceInfo)
        {
            // Kullanıcıyı kontrol et
            var userResult = _userService.GetById(userId);
            if (!userResult.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.UserNotFound);
            }

            // Kullanıcının bu şirkete erişim yetkisi olup olmadığını kontrol et
            var userCompanies = _userCompanyService.GetUserCompanies(userId);
            if (!userCompanies.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.UserCompanyNotFound);
            }

            if (!userCompanies.Data.Any(uc => uc.CompanyId == companyId && uc.IsActive == true))
            {
                return new ErrorDataResult<AccessToken>(Messages.UserCompanyAccessDenied);
            }

            // Kullanıcının aktif şirketini güncelle
            var updateResult = _userCompanyService.UpdateActiveCompany(userId, companyId);
            if (!updateResult.Success)
            {
                return new ErrorDataResult<AccessToken>(updateResult.Message);
            }

            // Kullanıcının rollerini al
            var defaultClaims = _userService.GetClaims(userResult.Data);
            var licensedRoles = _userLicenseService.GetUserRoles(userId).Data;

            // Tüm rolleri birleştir
            var allClaims = new List<OperationClaim>(defaultClaims);
            foreach (var role in licensedRoles)
            {
                if (!allClaims.Any(c => c.Name == role))
                {
                    allClaims.Add(new OperationClaim { Name = role });
                }
            }

            // Yeni token oluştur
            var accessToken = _tokenHelper.CreateToken(userResult.Data, allClaims, companyId);
            var refreshToken = _tokenHelper.CreateRefreshToken(userResult.Data);

            // Kullanıcının cihaz bilgilerini güncelle
            var userDevice = new UserDevice
            {
                UserId = userId,
                DeviceInfo = deviceInfo,
                RefreshToken = refreshToken.Token,
                LastIpAddress = GetCurrentIPAddress(),
                RefreshTokenExpiration = refreshToken.Expiration,
                IsActive = true,
                CreatedAt = DateTime.Now,
                LastUsedAt = DateTime.Now
            };

            _userDeviceService.Add(userDevice);

            accessToken.RefreshToken = refreshToken.Token;
            return new SuccessDataResult<AccessToken>(accessToken, Messages.CompanyChanged);
        }

        // Rate Limiting Methods
        public string GenerateDeviceFingerprint(string ipAddress, string userAgent, string deviceInfo)
        {
            return _advancedRateLimitService.GenerateDeviceFingerprint(ipAddress, userAgent, deviceInfo);
        }

        public IDataResult<int> GetRemainingLoginBanTime(string ipAddress, string deviceFingerprint)
        {
            return _advancedRateLimitService.GetRemainingLoginBanTime(ipAddress, deviceFingerprint);
        }

        public IDataResult<int> GetRemainingRegisterBanTime(string ipAddress)
        {
            return _advancedRateLimitService.GetRemainingRegisterBanTime(ipAddress);
        }
    }
}
